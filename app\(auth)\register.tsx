import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '@/components/Button';
import { TextField } from '@/components/TextField';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { ArrowLeft } from 'lucide-react-native';
import { Role } from '@/types';

export default function RegisterScreen() {
  const { colors } = useTheme();
  const { register, isLoading, error } = useAuth();
  
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [role, setRole] = useState<Role>('client');
  
  const [formErrors, setFormErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  
  const validateForm = () => {
    const errors = {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
    };
    let isValid = true;
    
    if (!firstName.trim()) {
      errors.firstName = 'First name is required';
      isValid = false;
    }
    
    if (!lastName.trim()) {
      errors.lastName = 'Last name is required';
      isValid = false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!emailRegex.test(email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }
    
    if (!password) {
      errors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
      isValid = false;
    }
    
    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }
    
    setFormErrors(errors);
    return isValid;
  };
  
  const handleRegister = () => {
    if (validateForm()) {
      register({ firstName, lastName, email, role }, password);
    }
  };
  
  const roles: { label: string; value: Role }[] = [
    { label: 'Client', value: 'client' },
    { label: 'Employee', value: 'employee' },
    { label: 'Manager', value: 'manager' },
  ];
  
  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView 
        style={[styles.container, { backgroundColor: colors.background }]}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
      >
        <StatusBar style={colors.background === '#FFFFFF' ? 'dark' : 'light'} />
        
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerContainer}>
          <Text style={[styles.title, { color: colors.text }]}>Create Account</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Sign up for a new BuildPro account
          </Text>
        </View>
        
        <View style={styles.formContainer}>
          <View style={styles.nameRow}>
            <TextField
              label="First Name"
              placeholder="Enter first name"
              value={firstName}
              onChangeText={setFirstName}
              error={formErrors.firstName}
              style={{ flex: 1, marginRight: 8 }}
            />
            
            <TextField
              label="Last Name"
              placeholder="Enter last name"
              value={lastName}
              onChangeText={setLastName}
              error={formErrors.lastName}
              style={{ flex: 1, marginLeft: 8 }}
            />
          </View>
          
          <TextField
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            error={formErrors.email}
          />
          
          <TextField
            label="Password"
            placeholder="Create password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            error={formErrors.password}
          />
          
          <TextField
            label="Confirm Password"
            placeholder="Confirm password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            error={formErrors.confirmPassword}
          />
          
          <Text style={[styles.roleLabel, { color: colors.text }]}>
            I am a:
          </Text>
          
          <View style={styles.roleButtons}>
            {roles.map((roleOption) => (
              <TouchableOpacity
                key={roleOption.value}
                style={[
                  styles.roleButton,
                  { 
                    backgroundColor: role === roleOption.value ? colors.primary : colors.card,
                    borderColor: role === roleOption.value ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => setRole(roleOption.value)}
              >
                <Text 
                  style={[
                    styles.roleButtonText, 
                    { 
                      color: role === roleOption.value ? 'white' : colors.text,
                      fontFamily: role === roleOption.value ? 'Inter-SemiBold' : 'Inter-Regular',
                    }
                  ]}
                >
                  {roleOption.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          {error && (
            <View style={styles.errorContainer}>
              <Text style={[styles.errorText, { color: colors.error }]}>
                {error}
              </Text>
            </View>
          )}
          
          <Button
            title="Create Account"
            onPress={handleRegister}
            variant="primary"
            size="lg"
            loading={isLoading}
            fullWidth
            style={{ marginTop: 24 }}
          />
          
          <TouchableOpacity
            onPress={() => router.push('/(auth)/login')}
            style={styles.loginLink}
          >
            <Text style={[styles.loginText, { color: colors.textSecondary }]}>
              Already have an account? <Text style={{ color: colors.primary }}>Sign In</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 8,
  },
  subtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  formContainer: {
    marginBottom: 32,
  },
  nameRow: {
    flexDirection: 'row',
  },
  roleLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    marginTop: 16,
    marginBottom: 12,
  },
  roleButtons: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  roleButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  roleButtonText: {
    fontSize: 14,
  },
  errorContainer: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#FEE2E2',
  },
  errorText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  loginLink: {
    marginTop: 16,
    alignItems: 'center',
  },
  loginText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
});