// Auth Types
export type Role = 'admin' | 'manager' | 'employee' | 'client';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: Role;
  avatar?: string;
}

// Client Types
export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  status: 'active' | 'inactive' | 'prospect';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Estimate Types
export interface EstimateItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  total: number;
}

export interface Estimate {
  id: string;
  title: string;
  clientId: string;
  clientName: string;
  status: 'draft' | 'sent' | 'approved' | 'declined' | 'expired';
  items: EstimateItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  total: number;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  expiresAt: string;
}

// Template Types
export interface Template {
  id: string;
  name: string;
  category: string;
  description: string;
  items: EstimateItem[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Dashboard Types
export interface DashboardStats {
  totalEstimates: number;
  pendingApprovals: number;
  activeClients: number;
  recentActivity: Activity[];
}

export interface Activity {
  id: string;
  type: 'estimate_created' | 'estimate_approved' | 'estimate_declined' | 'client_added';
  title: string;
  description: string;
  date: string;
  userId: string;
  userName: string;
}