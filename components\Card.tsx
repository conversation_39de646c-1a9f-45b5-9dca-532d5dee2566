import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle, TouchableOpacity } from 'react-native';
import { useTheme } from '@/context/ThemeContext';

interface CardProps {
  children: ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  elevation?: number;
}

export function Card({ children, style, onPress, elevation = 1 }: CardProps) {
  const { colors, isDarkMode } = useTheme();
  
  const getElevationStyle = () => {
    if (isDarkMode) {
      return {
        shadowOpacity: 0.3,
        shadowRadius: elevation * 3,
      };
    }
    
    return {
      shadowOpacity: 0.1,
      shadowRadius: elevation * 2,
    };
  };
  
  const cardStyle = [
    styles.card,
    {
      backgroundColor: colors.card,
      shadowColor: isDarkMode ? '#000000' : '#000000',
      shadowOffset: { width: 0, height: elevation },
      ...getElevationStyle(),
    },
    style,
  ];
  
  if (onPress) {
    return (
      <TouchableOpacity 
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>
    );
  }
  
  return <View style={cardStyle}>{children}</View>;
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
});