import React from 'react';
import { View, Text, StyleSheet, Image, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '@/components/Button';
import { useTheme } from '@/context/ThemeContext';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.primary }]}>
      <StatusBar style="light" />
      
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Image
            source={{ uri: 'https://images.pexels.com/photos/2760241/pexels-photo-2760241.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2' }}
            style={styles.backgroundImage}
          />
          <View style={[styles.overlay, { backgroundColor: colors.primary }]} />
          <View style={styles.logoTextContainer}>
            <Text style={styles.logoText}>BuildPro</Text>
            <Text style={styles.tagline}>Construction Estimates Made Easy</Text>
          </View>
        </View>
        
        <View style={styles.featuresContainer}>
          <FeatureItem 
            title="Create Detailed Estimates" 
            description="Build professional estimates with our easy-to-use wizard"
            color={colors.primaryLight}
            textColor={colors.primary}
          />
          <FeatureItem 
            title="Manage Clients" 
            description="Keep track of all your clients and their projects"
            color={colors.secondaryLight}
            textColor={colors.secondary}
          />
          <FeatureItem 
            title="Save Templates" 
            description="Save time with reusable estimate templates"
            color={colors.successLight}
            textColor={colors.success}
          />
        </View>
        
        <View style={styles.buttonsContainer}>
          <Button
            title="Sign In"
            onPress={() => router.push('/(auth)/login')}
            variant="outline"
            size="lg"
            fullWidth
            style={{ marginBottom: 16, backgroundColor: 'transparent', borderColor: '#FFFFFF' }}
            textStyle={{ color: '#FFFFFF' }}
          />
          <Button
            title="Create Account"
            onPress={() => router.push('/(auth)/register')}
            variant="secondary"
            size="lg"
            fullWidth
          />
        </View>
      </View>
    </View>
  );
}

interface FeatureItemProps {
  title: string;
  description: string;
  color: string;
  textColor: string;
}

function FeatureItem({ title, description, color, textColor }: FeatureItemProps) {
  return (
    <View style={[styles.featureItem, { backgroundColor: color }]}>
      <Text style={[styles.featureTitle, { color: textColor }]}>{title}</Text>
      <Text style={[styles.featureDescription, { color: textColor }]}>{description}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  logoContainer: {
    height: height * 0.4,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  overlay: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    opacity: 0.7,
  },
  logoTextContainer: {
    alignItems: 'center',
  },
  logoText: {
    fontFamily: 'Inter-Bold',
    fontSize: 48,
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 2 },
    textShadowRadius: 3,
  },
  tagline: {
    fontFamily: 'Inter-Medium',
    fontSize: 18,
    color: '#FFFFFF',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  featuresContainer: {
    marginBottom: 32,
  },
  featureItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  featureTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 4,
  },
  featureDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    opacity: 0.8,
  },
  buttonsContainer: {
    marginTop: 'auto',
  },
});