import { Stack } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';

export default function EstimatesLayout() {
  const { colors } = useTheme();
  
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTitleStyle: {
          fontFamily: 'Inter-SemiBold',
          color: colors.text,
        },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Estimates',
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: 'Estimate Details',
        }}
      />
      <Stack.Screen
        name="create"
        options={{
          title: 'Create Estimate',
        }}
      />
    </Stack>
  );
}