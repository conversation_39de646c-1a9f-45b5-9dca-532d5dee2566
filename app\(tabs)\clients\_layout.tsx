import { Stack } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';

export default function ClientsLayout() {
  const { colors } = useTheme();
  
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTitleStyle: {
          fontFamily: 'Inter-SemiBold',
          color: colors.text,
        },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Clients',
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: 'Client Details',
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: 'Add Client',
          presentation: 'modal',
        }}
      />
    </Stack>
  );
}