export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          first_name: string
          last_name: string
          role: 'admin' | 'manager' | 'employee' | 'client'
          avatar_url: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          email: string
          first_name: string
          last_name: string
          role: 'admin' | 'manager' | 'employee' | 'client'
          avatar_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          first_name?: string
          last_name?: string
          role?: 'admin' | 'manager' | 'employee' | 'client'
          avatar_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      clients: {
        Row: {
          id: string
          name: string
          email: string
          phone: string | null
          address: string | null
          city: string | null
          state: string | null
          zip_code: string | null
          status: 'active' | 'inactive' | 'prospect'
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          zip_code?: string | null
          status: 'active' | 'inactive' | 'prospect'
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          zip_code?: string | null
          status?: 'active' | 'inactive' | 'prospect'
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      estimates: {
        Row: {
          id: string
          title: string
          client_id: string | null
          status: 'draft' | 'sent' | 'approved' | 'declined' | 'expired'
          subtotal: number
          tax_rate: number
          tax_amount: number
          total: number
          notes: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
          expires_at: string | null
        }
        Insert: {
          id?: string
          title: string
          client_id?: string | null
          status: 'draft' | 'sent' | 'approved' | 'declined' | 'expired'
          subtotal?: number
          tax_rate?: number
          tax_amount?: number
          total?: number
          notes?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          expires_at?: string | null
        }
        Update: {
          id?: string
          title?: string
          client_id?: string | null
          status?: 'draft' | 'sent' | 'approved' | 'declined' | 'expired'
          subtotal?: number
          tax_rate?: number
          tax_amount?: number
          total?: number
          notes?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          expires_at?: string | null
        }
      }
      estimate_items: {
        Row: {
          id: string
          estimate_id: string | null
          name: string
          description: string | null
          quantity: number
          unit: string
          unit_price: number
          total: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          estimate_id?: string | null
          name: string
          description?: string | null
          quantity?: number
          unit: string
          unit_price?: number
          total?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          estimate_id?: string | null
          name?: string
          description?: string | null
          quantity?: number
          unit?: string
          unit_price?: number
          total?: number
          created_at?: string | null
          updated_at?: string | null
        }
      }
      templates: {
        Row: {
          id: string
          name: string
          category: string
          description: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          category: string
          description?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          category?: string
          description?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      template_items: {
        Row: {
          id: string
          template_id: string | null
          name: string
          description: string | null
          quantity: number
          unit: string
          unit_price: number
          total: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          template_id?: string | null
          name: string
          description?: string | null
          quantity?: number
          unit: string
          unit_price?: number
          total?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          template_id?: string | null
          name?: string
          description?: string | null
          quantity?: number
          unit?: string
          unit_price?: number
          total?: number
          created_at?: string | null
          updated_at?: string | null
        }
      }
    }
  }
}