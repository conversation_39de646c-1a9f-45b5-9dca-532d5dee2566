import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { router } from 'expo-router';
import { Card } from '@/components/Card';
import { StatusBadge } from '@/components/StatusBadge';
import { Button } from '@/components/Button';
import { useTheme } from '@/context/ThemeContext';
import { mockClients } from '@/data/mockData';
import { Client } from '@/types';
import { Plus, Search, CircleUser as UserCircle, Mail, Phone, MapPin } from 'lucide-react-native';

export default function ClientsScreen() {
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  
  const filteredClients = mockClients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          client.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus ? client.status === filterStatus : true;
    return matchesSearch && matchesFilter;
  });
  
  const filterOptions = [
    { label: 'All', value: null },
    { label: 'Active', value: 'active' },
    { label: 'Prospect', value: 'prospect' },
    { label: 'Inactive', value: 'inactive' },
  ];
  
  const renderClientItem = ({ item }: { item: Client }) => (
    <Card 
      style={styles.clientCard} 
      onPress={() => router.push(`/clients/${item.id}`)}
    >
      <View style={styles.clientHeader}>
        <View style={styles.clientNameContainer}>
          <UserCircle size={24} color={colors.primary} style={styles.clientIcon} />
          <Text style={[styles.clientName, { color: colors.text }]}>{item.name}</Text>
        </View>
        <StatusBadge status={item.status} />
      </View>
      
      <View style={styles.divider} />
      
      <View style={styles.clientDetailsContainer}>
        <View style={styles.clientDetailRow}>
          <Mail size={16} color={colors.textSecondary} style={styles.detailIcon} />
          <Text style={[styles.clientDetailText, { color: colors.text }]}>{item.email}</Text>
        </View>
        
        <View style={styles.clientDetailRow}>
          <Phone size={16} color={colors.textSecondary} style={styles.detailIcon} />
          <Text style={[styles.clientDetailText, { color: colors.text }]}>{item.phone}</Text>
        </View>
        
        <View style={styles.clientDetailRow}>
          <MapPin size={16} color={colors.textSecondary} style={styles.detailIcon} />
          <Text style={[styles.clientDetailText, { color: colors.text }]} numberOfLines={1}>
            {item.city}, {item.state} {item.zipCode}
          </Text>
        </View>
      </View>
    </Card>
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={[styles.searchContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search clients..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <Button
          title="Add Client"
          onPress={() => router.push('/clients/add')}
          variant="primary"
          leftIcon={<Plus size={16} color="#FFFFFF" />}
        />
      </View>
      
      <View style={styles.filtersContainer}>
        {filterOptions.map(option => (
          <TouchableOpacity
            key={option.label}
            style={[
              styles.filterButton,
              { 
                backgroundColor: filterStatus === option.value ? colors.primary : colors.card,
                borderColor: filterStatus === option.value ? colors.primary : colors.border,
              }
            ]}
            onPress={() => setFilterStatus(option.value)}
          >
            <Text
              style={[
                styles.filterButtonText,
                { color: filterStatus === option.value ? '#FFFFFF' : colors.text }
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <FlatList
        data={filteredClients}
        renderItem={renderClientItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No clients found. Try adjusting your filters or add a new client.
            </Text>
            <Button
              title="Add Your First Client"
              onPress={() => router.push('/clients/add')}
              variant="primary"
              style={{ marginTop: 16 }}
            />
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  listContainer: {
    paddingBottom: 16,
  },
  clientCard: {
    marginBottom: 12,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientIcon: {
    marginRight: 8,
  },
  clientName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  divider: {
    height: 1,
    backgroundColor: '#E2E8F0',
    marginBottom: 12,
  },
  clientDetailsContainer: {
    gap: 8,
  },
  clientDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailIcon: {
    marginRight: 8,
  },
  clientDetailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
});