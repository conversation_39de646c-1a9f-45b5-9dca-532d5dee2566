export const theme = {
  light: {
    primary: '#2563EB',     // Blue
    primaryLight: '#DBEAFE', // Light blue
    primaryDark: '#1E40AF',  // Dark blue
    
    secondary: '#D97706',    // Amber
    secondaryLight: '#FEF3C7', // Light amber
    secondaryDark: '#92400E',  // Dark amber
    
    accent: '#475569',       // Slate
    accentLight: '#F8FAFC',  // Light slate
    accentDark: '#1E293B',   // Dark slate
    
    success: '#16A34A',      // Green
    successLight: '#DCFCE7', // Light green
    
    warning: '#EAB308',      // Yellow
    warningLight: '#FEF9C3', // Light yellow
    
    error: '#DC2626',        // Red
    errorLight: '#FEE2E2',   // Light red
    
    background: '#FFFFFF',   // White
    card: '#F8FAFC',         // Very light gray
    border: '#E2E8F0',       // Light gray
    text: '#0F172A',         // Very dark blue/gray
    textSecondary: '#64748B', // Medium gray
  },
  dark: {
    primary: '#3B82F6',     // Lighter blue for dark mode
    primaryLight: '#1E3A8A', // Darker blue
    primaryDark: '#93C5FD',  // Very light blue
    
    secondary: '#F59E0B',    // Lighter amber for dark mode
    secondaryLight: '#78350F', // Very dark amber
    secondaryDark: '#FCD34D',  // Light amber
    
    accent: '#64748B',       // Medium slate
    accentLight: '#0F172A',  // Very dark slate
    accentDark: '#CBD5E1',   // Light slate
    
    success: '#22C55E',      // Lighter green for dark mode
    successLight: '#14532D', // Dark green
    
    warning: '#FBBF24',      // Brighter yellow for dark mode
    warningLight: '#713F12', // Dark yellow
    
    error: '#EF4444',        // Brighter red for dark mode
    errorLight: '#7F1D1D',   // Dark red
    
    background: '#0F172A',   // Very dark blue/gray
    card: '#1E293B',         // Dark blue/gray
    border: '#334155',       // Medium dark blue/gray
    text: '#F8FAFC',         // Very light gray
    textSecondary: '#94A3B8', // Medium light gray
  }
};

export type ThemeType = typeof theme.light;