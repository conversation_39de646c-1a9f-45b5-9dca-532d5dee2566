import { Stack } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';

export default function TemplatesLayout() {
  const { colors } = useTheme();
  
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTitleStyle: {
          fontFamily: 'Inter-SemiBold',
          color: colors.text,
        },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Templates',
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: 'Template Details',
        }}
      />
      <Stack.Screen
        name="create"
        options={{
          title: 'Create Template',
        }}
      />
    </Stack>
  );
}