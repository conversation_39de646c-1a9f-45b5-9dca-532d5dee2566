import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/context/ThemeContext';

interface StatusBadgeProps {
  status: string;
  size?: 'sm' | 'md' | 'lg';
}

export function StatusBadge({ status, size = 'md' }: StatusBadgeProps) {
  const { colors } = useTheme();
  
  const getStatusColor = () => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'approved':
        return {
          bg: colors.successLight,
          text: colors.success,
        };
      case 'inactive':
      case 'expired':
        return {
          bg: colors.errorLight,
          text: colors.error,
        };
      case 'prospect':
      case 'draft':
        return {
          bg: colors.primaryLight,
          text: colors.primary,
        };
      case 'sent':
      case 'pending':
        return {
          bg: colors.warningLight,
          text: colors.warning,
        };
      case 'declined':
        return {
          bg: colors.errorLight,
          text: colors.error,
        };
      default:
        return {
          bg: colors.accentLight,
          text: colors.accent,
        };
    }
  };
  
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          paddingVertical: 2,
          paddingHorizontal: 6,
          fontSize: 10,
        };
      case 'lg':
        return {
          paddingVertical: 6,
          paddingHorizontal: 12,
          fontSize: 14,
        };
      default:
        return {
          paddingVertical: 4,
          paddingHorizontal: 8,
          fontSize: 12,
        };
    }
  };
  
  const { bg, text } = getStatusColor();
  const sizeStyles = getSizeStyles();
  
  return (
    <View style={[
      styles.badge, 
      { backgroundColor: bg, paddingVertical: sizeStyles.paddingVertical, paddingHorizontal: sizeStyles.paddingHorizontal }
    ]}>
      <Text style={[styles.text, { color: text, fontSize: sizeStyles.fontSize }]}>
        {status}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  badge: {
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  text: {
    fontWeight: '600',
    textTransform: 'capitalize',
  },
});