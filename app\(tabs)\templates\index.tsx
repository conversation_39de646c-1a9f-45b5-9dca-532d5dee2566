import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { router } from 'expo-router';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { useTheme } from '@/context/ThemeContext';
import { mockTemplates } from '@/data/mockData';
import { Template } from '@/types';
import { Plus, Search, ListChecks, Calendar } from 'lucide-react-native';

export default function TemplatesScreen() {
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  
  // Get unique categories
  const categories = Array.from(new Set(mockTemplates.map(template => template.category)));
  
  const filteredTemplates = mockTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterCategory ? template.category === filterCategory : true;
    return matchesSearch && matchesFilter;
  });
  
  const filterOptions = [
    { label: 'All', value: null },
    ...categories.map(category => ({ label: category, value: category })),
  ];
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };
  
  const renderTemplateItem = ({ item }: { item: Template }) => (
    <Card 
      style={styles.templateCard} 
      onPress={() => router.push(`/templates/${item.id}`)}
    >
      <View style={styles.templateHeader}>
        <Text style={[styles.templateTitle, { color: colors.text }]}>{item.name}</Text>
        <View 
          style={[
            styles.categoryBadge, 
            { backgroundColor: colors.primaryLight }
          ]}
        >
          <Text style={[styles.categoryText, { color: colors.primary }]}>{item.category}</Text>
        </View>
      </View>
      
      <Text 
        style={[styles.templateDescription, { color: colors.textSecondary }]}
        numberOfLines={2}
      >
        {item.description}
      </Text>
      
      <View style={styles.divider} />
      
      <View style={styles.templateDetails}>
        <View style={styles.detailItem}>
          <ListChecks size={16} color={colors.textSecondary} style={styles.detailIcon} />
          <Text style={[styles.detailText, { color: colors.textSecondary }]}>
            {item.items.length} {item.items.length === 1 ? 'item' : 'items'}
          </Text>
        </View>
        
        <View style={styles.detailItem}>
          <Calendar size={16} color={colors.textSecondary} style={styles.detailIcon} />
          <Text style={[styles.detailText, { color: colors.textSecondary }]}>
            Updated {formatDate(item.updatedAt)}
          </Text>
        </View>
      </View>
    </Card>
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={[styles.searchContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search templates..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <Button
          title="New"
          onPress={() => router.push('/templates/create')}
          variant="primary"
          leftIcon={<Plus size={16} color="#FFFFFF" />}
        />
      </View>
      
      <View style={styles.filtersContainer}>
        {filterOptions.map(option => (
          <TouchableOpacity
            key={option.label}
            style={[
              styles.filterButton,
              { 
                backgroundColor: filterCategory === option.value ? colors.primary : colors.card,
                borderColor: filterCategory === option.value ? colors.primary : colors.border,
              }
            ]}
            onPress={() => setFilterCategory(option.value)}
          >
            <Text
              style={[
                styles.filterButtonText,
                { color: filterCategory === option.value ? '#FFFFFF' : colors.text }
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <FlatList
        data={filteredTemplates}
        renderItem={renderTemplateItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No templates found. Try adjusting your filters or create a new template.
            </Text>
            <Button
              title="Create New Template"
              onPress={() => router.push('/templates/create')}
              variant="primary"
              style={{ marginTop: 16 }}
            />
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  listContainer: {
    paddingBottom: 16,
  },
  templateCard: {
    marginBottom: 12,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    marginRight: 8,
  },
  categoryBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
  },
  categoryText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  templateDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#E2E8F0',
    marginBottom: 12,
  },
  templateDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailIcon: {
    marginRight: 6,
  },
  detailText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
});