import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card } from '@/components/Card';
import { StatCard } from '@/components/StatCard';
import { StatusBadge } from '@/components/StatusBadge';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { mockDashboardStats, mockEstimates, mockClients } from '@/data/mockData';
import { Router } from 'expo-router';
import { FileText, Users, Clock, CircleCheck as CheckCircle, Calendar, Bell } from 'lucide-react-native';

export default function DashboardScreen() {
  const { colors } = useTheme();
  const { user } = useAuth();
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'estimate_created':
        return <FileText size={16} color={colors.primary} />;
      case 'estimate_approved':
        return <CheckCircle size={16} color={colors.success} />;
      case 'estimate_declined':
        return <Clock size={16} color={colors.error} />;
      case 'client_added':
        return <Users size={16} color={colors.secondary} />;
      default:
        return <Bell size={16} color={colors.textSecondary} />;
    }
  };
  
  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.welcomeSection}>
        <Text style={[styles.welcomeText, { color: colors.text }]}>
          Welcome back, {user?.firstName || 'User'}!
        </Text>
        <Text style={[styles.dateText, { color: colors.textSecondary }]}>
          {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
        </Text>
      </View>
      
      <ScrollView 
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.statsContainer}
        style={styles.statsScrollView}
      >
        <StatCard 
          title="Total Estimates" 
          value={mockDashboardStats.totalEstimates}
          icon={<FileText size={20} color={colors.primary} />}
          change={{ value: 12, isPositive: true }}
          style={{ marginRight: 12 }}
        />
        <StatCard 
          title="Pending Approvals" 
          value={mockDashboardStats.pendingApprovals}
          icon={<Clock size={20} color={colors.warning} />}
          change={{ value: 3, isPositive: true }}
          style={{ marginRight: 12 }}
        />
        <StatCard 
          title="Active Clients" 
          value={mockDashboardStats.activeClients}
          icon={<Users size={20} color={colors.secondary} />}
          change={{ value: 5, isPositive: true }}
        />
      </ScrollView>
      
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Estimates</Text>
        <TouchableOpacity>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
        </TouchableOpacity>
      </View>
      
      {mockEstimates.slice(0, 3).map((estimate) => (
        <Card key={estimate.id} style={styles.estimateCard} onPress={() => {}}>
          <View style={styles.estimateHeader}>
            <Text style={[styles.estimateTitle, { color: colors.text }]} numberOfLines={1}>
              {estimate.title}
            </Text>
            <StatusBadge status={estimate.status} />
          </View>
          
          <View style={styles.estimateDetails}>
            <View style={styles.estimateDetail}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Client:</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>{estimate.clientName}</Text>
            </View>
            <View style={styles.estimateDetail}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Total:</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                ${estimate.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </Text>
            </View>
            <View style={styles.estimateDetail}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Created:</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {formatDate(estimate.createdAt)}
              </Text>
            </View>
          </View>
        </Card>
      ))}
      
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Activity</Text>
      </View>
      
      <Card style={styles.activityCard}>
        {mockDashboardStats.recentActivity.map((activity) => (
          <View key={activity.id} style={styles.activityItem}>
            <View style={[styles.activityIcon, { backgroundColor: colors.primaryLight }]}>
              {getActivityIcon(activity.type)}
            </View>
            <View style={styles.activityContent}>
              <Text style={[styles.activityTitle, { color: colors.text }]}>
                {activity.title}
              </Text>
              <Text style={[styles.activityDescription, { color: colors.textSecondary }]}>
                {activity.description}
              </Text>
            </View>
            <Text style={[styles.activityDate, { color: colors.textSecondary }]}>
              {formatDate(activity.date)}
            </Text>
          </View>
        ))}
      </Card>
      
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Clients</Text>
        <TouchableOpacity>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
        </TouchableOpacity>
      </View>
      
      {mockClients.slice(0, 3).map((client) => (
        <Card key={client.id} style={styles.clientCard} onPress={() => {}}>
          <View style={styles.clientHeader}>
            <Text style={[styles.clientName, { color: colors.text }]}>{client.name}</Text>
            <StatusBadge status={client.status} />
          </View>
          
          <View style={styles.clientDetails}>
            <Text style={[styles.clientDetail, { color: colors.textSecondary }]}>
              {client.email} • {client.phone}
            </Text>
            <Text style={[styles.clientDetail, { color: colors.textSecondary }]}>
              {client.city}, {client.state}
            </Text>
          </View>
        </Card>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  welcomeSection: {
    marginBottom: 24,
  },
  welcomeText: {
    fontFamily: 'Inter-Bold',
    fontSize: 24,
  },
  dateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginTop: 4,
  },
  statsScrollView: {
    marginBottom: 24,
  },
  statsContainer: {
    paddingRight: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
  },
  viewAllText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  estimateCard: {
    marginBottom: 12,
  },
  estimateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  estimateTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    flex: 1,
    marginRight: 8,
  },
  estimateDetails: {
    gap: 4,
  },
  estimateDetail: {
    flexDirection: 'row',
  },
  detailLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginRight: 4,
  },
  detailValue: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  activityCard: {
    marginBottom: 24,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  activityDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 13,
  },
  activityDate: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  clientCard: {
    marginBottom: 12,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clientName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  clientDetails: {
    gap: 4,
  },
  clientDetail: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
});