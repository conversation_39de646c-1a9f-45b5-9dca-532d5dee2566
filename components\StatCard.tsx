import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Card } from './Card';
import { useTheme } from '@/context/ThemeContext';

interface StatCardProps {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
  color?: string;
  change?: {
    value: number;
    isPositive: boolean;
  };
  style?: ViewStyle;
}

export function StatCard({ 
  title, 
  value, 
  icon, 
  color,
  change, 
  style 
}: StatCardProps) {
  const { colors } = useTheme();
  
  return (
    <Card style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.textSecondary }]}>{title}</Text>
        {icon && <View style={styles.iconContainer}>{icon}</View>}
      </View>
      
      <Text style={[styles.value, { color: colors.text }]}>{value}</Text>
      
      {change && (
        <View style={styles.changeContainer}>
          <View 
            style={[
              styles.changeIndicator, 
              { 
                backgroundColor: change.isPositive ? colors.successLight : colors.errorLight,
              }
            ]}
          >
            <Text 
              style={[
                styles.changeText, 
                { 
                  color: change.isPositive ? colors.success : colors.error,
                }
              ]}
            >
              {change.isPositive ? '+' : ''}{change.value}%
            </Text>
          </View>
          <Text style={[styles.changePeriod, { color: colors.textSecondary }]}>
            vs last month
          </Text>
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    minWidth: 160,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  value: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeIndicator: {
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 8,
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  changePeriod: {
    fontSize: 12,
  },
});