import { User, Client, Estimate, Template, Activity } from '@/types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'admin',
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'manager',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'employee',
    avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 'user-4',
    email: '<EMAIL>',
    firstName: 'Jessica',
    lastName: 'Brown',
    role: 'client',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
];

// Mock Clients
export const mockClients: Client[] = [
  {
    id: 'client-1',
    name: 'Riverside Homes',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Main St',
    city: 'Austin',
    state: 'TX',
    zipCode: '78701',
    status: 'active',
    notes: 'Building custom homes in Lakeway area.',
    createdAt: '2023-05-15T10:30:00Z',
    updatedAt: '2023-09-20T14:15:00Z',
  },
  {
    id: 'client-2',
    name: 'Modern Living Properties',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Oak Ave',
    city: 'Portland',
    state: 'OR',
    zipCode: '97201',
    status: 'active',
    notes: 'Multiple renovation projects planned for 2023.',
    createdAt: '2023-06-22T09:45:00Z',
    updatedAt: '2023-10-05T11:20:00Z',
  },
  {
    id: 'client-3',
    name: 'Green Valley Developments',
    email: '<EMAIL>',
    phone: '(*************',
    address: '789 Pine Rd',
    city: 'Denver',
    state: 'CO',
    zipCode: '80202',
    status: 'prospect',
    notes: 'Interested in eco-friendly building materials.',
    createdAt: '2023-08-10T15:20:00Z',
    updatedAt: '2023-10-12T16:30:00Z',
  },
  {
    id: 'client-4',
    name: 'Highland Construction',
    email: '<EMAIL>',
    phone: '(*************',
    address: '101 Mountain View',
    city: 'Seattle',
    state: 'WA',
    zipCode: '98101',
    status: 'inactive',
    notes: 'Previous projects completed in 2022.',
    createdAt: '2022-11-05T08:15:00Z',
    updatedAt: '2023-01-20T10:45:00Z',
  },
  {
    id: 'client-5',
    name: 'Coastal Renovations',
    email: '<EMAIL>',
    phone: '(*************',
    address: '222 Beach Blvd',
    city: 'San Diego',
    state: 'CA',
    zipCode: '92101',
    status: 'active',
    notes: 'Specializes in beachfront property renovations.',
    createdAt: '2023-03-18T13:40:00Z',
    updatedAt: '2023-09-28T09:10:00Z',
  },
];

// Mock Estimates
export const mockEstimates: Estimate[] = [
  {
    id: 'est-1',
    title: 'Kitchen Renovation - Riverside Homes',
    clientId: 'client-1',
    clientName: 'Riverside Homes',
    status: 'approved',
    items: [
      {
        id: 'item-1',
        name: 'Demo & Removal',
        description: 'Remove existing cabinets, countertops, and flooring',
        quantity: 1,
        unit: 'job',
        unitPrice: 2500,
        total: 2500,
      },
      {
        id: 'item-2',
        name: 'Custom Cabinets',
        description: 'Solid wood cabinets with soft-close hinges',
        quantity: 15,
        unit: 'linear ft',
        unitPrice: 450,
        total: 6750,
      },
      {
        id: 'item-3',
        name: 'Quartz Countertops',
        description: 'Premium quartz with undermount sink',
        quantity: 35,
        unit: 'sq ft',
        unitPrice: 85,
        total: 2975,
      },
      {
        id: 'item-4',
        name: 'Backsplash Tile',
        description: 'Ceramic subway tile installation',
        quantity: 30,
        unit: 'sq ft',
        unitPrice: 40,
        total: 1200,
      },
      {
        id: 'item-5',
        name: 'Electrical Work',
        description: 'Upgrade outlets, install under-cabinet lighting',
        quantity: 1,
        unit: 'job',
        unitPrice: 1800,
        total: 1800,
      },
    ],
    subtotal: 15225,
    taxRate: 0.0825,
    taxAmount: 1256.06,
    total: 16481.06,
    notes: 'Project scheduled to begin June 15th.',
    createdBy: 'user-1',
    createdAt: '2023-05-20T11:30:00Z',
    updatedAt: '2023-05-25T14:45:00Z',
    expiresAt: '2023-06-20T11:30:00Z',
  },
  {
    id: 'est-2',
    title: 'Office Remodel - Modern Living',
    clientId: 'client-2',
    clientName: 'Modern Living Properties',
    status: 'sent',
    items: [
      {
        id: 'item-1',
        name: 'Interior Demolition',
        description: 'Remove existing walls and fixtures',
        quantity: 1,
        unit: 'job',
        unitPrice: 3200,
        total: 3200,
      },
      {
        id: 'item-2',
        name: 'Framing',
        description: 'New wall framing and door installation',
        quantity: 1,
        unit: 'job',
        unitPrice: 4500,
        total: 4500,
      },
      {
        id: 'item-3',
        name: 'Drywall',
        description: 'Install and finish drywall',
        quantity: 850,
        unit: 'sq ft',
        unitPrice: 3.75,
        total: 3187.5,
      },
      {
        id: 'item-4',
        name: 'Flooring',
        description: 'Commercial-grade vinyl plank flooring',
        quantity: 750,
        unit: 'sq ft',
        unitPrice: 5.5,
        total: 4125,
      },
      {
        id: 'item-5',
        name: 'Painting',
        description: 'Prime and paint walls and trim',
        quantity: 1,
        unit: 'job',
        unitPrice: 3800,
        total: 3800,
      },
    ],
    subtotal: 18812.5,
    taxRate: 0.0725,
    taxAmount: 1363.91,
    total: 20176.41,
    notes: 'Includes all labor and materials as discussed.',
    createdBy: 'user-2',
    createdAt: '2023-09-10T09:15:00Z',
    updatedAt: '2023-09-15T16:20:00Z',
    expiresAt: '2023-10-10T09:15:00Z',
  },
  {
    id: 'est-3',
    title: 'Solar Panel Installation - Green Valley',
    clientId: 'client-3',
    clientName: 'Green Valley Developments',
    status: 'draft',
    items: [
      {
        id: 'item-1',
        name: 'Solar Panels',
        description: '400W high-efficiency panels',
        quantity: 24,
        unit: 'each',
        unitPrice: 350,
        total: 8400,
      },
      {
        id: 'item-2',
        name: 'Inverter',
        description: '10kW grid-tie inverter',
        quantity: 1,
        unit: 'each',
        unitPrice: 2800,
        total: 2800,
      },
      {
        id: 'item-3',
        name: 'Mounting Hardware',
        description: 'Roof mounting system',
        quantity: 1,
        unit: 'job',
        unitPrice: 1500,
        total: 1500,
      },
      {
        id: 'item-4',
        name: 'Installation Labor',
        description: 'Professional installation team',
        quantity: 40,
        unit: 'hours',
        unitPrice: 85,
        total: 3400,
      },
    ],
    subtotal: 16100,
    taxRate: 0.08,
    taxAmount: 1288,
    total: 17388,
    notes: 'Draft estimate for initial client review.',
    createdBy: 'user-3',
    createdAt: '2023-10-05T14:10:00Z',
    updatedAt: '2023-10-08T11:30:00Z',
    expiresAt: '2023-11-05T14:10:00Z',
  },
];

// Mock Templates
export const mockTemplates: Template[] = [
  {
    id: 'template-1',
    name: 'Kitchen Renovation Standard',
    category: 'Interior',
    description: 'Standard kitchen renovation template with common line items',
    items: [
      {
        id: 'item-1',
        name: 'Demo & Removal',
        description: 'Remove existing cabinets, countertops, and flooring',
        quantity: 1,
        unit: 'job',
        unitPrice: 2500,
        total: 2500,
      },
      {
        id: 'item-2',
        name: 'Cabinets',
        description: 'Standard cabinets with hardware',
        quantity: 15,
        unit: 'linear ft',
        unitPrice: 350,
        total: 5250,
      },
      {
        id: 'item-3',
        name: 'Countertops',
        description: 'Laminate countertops',
        quantity: 35,
        unit: 'sq ft',
        unitPrice: 45,
        total: 1575,
      },
      {
        id: 'item-4',
        name: 'Backsplash',
        description: 'Ceramic tile installation',
        quantity: 30,
        unit: 'sq ft',
        unitPrice: 35,
        total: 1050,
      },
      {
        id: 'item-5',
        name: 'Electrical',
        description: 'Basic electrical updates',
        quantity: 1,
        unit: 'job',
        unitPrice: 1200,
        total: 1200,
      },
    ],
    createdBy: 'user-1',
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2023-03-20T14:30:00Z',
  },
  {
    id: 'template-2',
    name: 'Bathroom Remodel Basic',
    category: 'Interior',
    description: 'Basic bathroom remodel template for standard bathrooms',
    items: [
      {
        id: 'item-1',
        name: 'Demo',
        description: 'Remove existing fixtures and tile',
        quantity: 1,
        unit: 'job',
        unitPrice: 1800,
        total: 1800,
      },
      {
        id: 'item-2',
        name: 'Plumbing',
        description: 'Update plumbing fixtures and connections',
        quantity: 1,
        unit: 'job',
        unitPrice: 2500,
        total: 2500,
      },
      {
        id: 'item-3',
        name: 'Tile Work',
        description: 'Floor and shower tile installation',
        quantity: 120,
        unit: 'sq ft',
        unitPrice: 18,
        total: 2160,
      },
      {
        id: 'item-4',
        name: 'Vanity',
        description: 'Standard vanity with sink and faucet',
        quantity: 1,
        unit: 'each',
        unitPrice: 850,
        total: 850,
      },
      {
        id: 'item-5',
        name: 'Toilet',
        description: 'Standard toilet installation',
        quantity: 1,
        unit: 'each',
        unitPrice: 350,
        total: 350,
      },
    ],
    createdBy: 'user-2',
    createdAt: '2023-02-10T11:30:00Z',
    updatedAt: '2023-04-15T09:45:00Z',
  },
  {
    id: 'template-3',
    name: 'Roof Replacement',
    category: 'Exterior',
    description: 'Standard roof replacement template with common materials',
    items: [
      {
        id: 'item-1',
        name: 'Tear Off',
        description: 'Remove existing roofing materials',
        quantity: 1,
        unit: 'square',
        unitPrice: 85,
        total: 85,
      },
      {
        id: 'item-2',
        name: 'Shingles',
        description: 'Architectural shingles installation',
        quantity: 25,
        unit: 'square',
        unitPrice: 350,
        total: 8750,
      },
      {
        id: 'item-3',
        name: 'Underlayment',
        description: 'Synthetic underlayment',
        quantity: 25,
        unit: 'square',
        unitPrice: 45,
        total: 1125,
      },
      {
        id: 'item-4',
        name: 'Flashing',
        description: 'New flashing installation',
        quantity: 1,
        unit: 'job',
        unitPrice: 800,
        total: 800,
      },
      {
        id: 'item-5',
        name: 'Disposal',
        description: 'Debris removal and disposal',
        quantity: 1,
        unit: 'job',
        unitPrice: 650,
        total: 650,
      },
    ],
    createdBy: 'user-1',
    createdAt: '2023-03-05T13:15:00Z',
    updatedAt: '2023-05-10T16:30:00Z',
  },
];

// Mock Activities
export const mockActivities: Activity[] = [
  {
    id: 'activity-1',
    type: 'estimate_created',
    title: 'Estimate Created',
    description: 'Kitchen Renovation - Riverside Homes',
    date: '2023-05-20T11:30:00Z',
    userId: 'user-1',
    userName: 'John Smith',
  },
  {
    id: 'activity-2',
    type: 'estimate_approved',
    title: 'Estimate Approved',
    description: 'Kitchen Renovation - Riverside Homes',
    date: '2023-05-25T14:45:00Z',
    userId: 'user-1',
    userName: 'John Smith',
  },
  {
    id: 'activity-3',
    type: 'client_added',
    title: 'Client Added',
    description: 'Green Valley Developments',
    date: '2023-08-10T15:20:00Z',
    userId: 'user-2',
    userName: 'Sarah Johnson',
  },
  {
    id: 'activity-4',
    type: 'estimate_created',
    title: 'Estimate Created',
    description: 'Office Remodel - Modern Living',
    date: '2023-09-10T09:15:00Z',
    userId: 'user-2',
    userName: 'Sarah Johnson',
  },
  {
    id: 'activity-5',
    type: 'estimate_created',
    title: 'Estimate Created',
    description: 'Solar Panel Installation - Green Valley',
    date: '2023-10-05T14:10:00Z',
    userId: 'user-3',
    userName: 'Mike Taylor',
  },
];

// Mock Dashboard Stats
export const mockDashboardStats = {
  totalEstimates: 18,
  pendingApprovals: 5,
  activeClients: 12,
  recentActivity: mockActivities,
};