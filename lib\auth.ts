import { supabase } from './supabase';
import { User, Role } from '@/types';

/**
 * Auth utility functions for common authentication operations
 */

/**
 * Get the current user's profile from the users table
 */
export async function getCurrentUserProfile(): Promise<User | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return null;

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }

    return {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      role: data.role as Role,
      avatar: data.avatar_url || undefined,
    };
  } catch (error) {
    console.error('Error getting current user profile:', error);
    return null;
  }
}

/**
 * Update the current user's profile
 */
export async function updateUserProfile(updates: Partial<Omit<User, 'id' | 'email'>>): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return false;

    const updateData: any = {};
    
    if (updates.firstName !== undefined) updateData.first_name = updates.firstName;
    if (updates.lastName !== undefined) updateData.last_name = updates.lastName;
    if (updates.role !== undefined) updateData.role = updates.role;
    if (updates.avatar !== undefined) updateData.avatar_url = updates.avatar;

    const { error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', user.id);

    if (error) {
      console.error('Error updating user profile:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating user profile:', error);
    return false;
  }
}

/**
 * Check if the current user has a specific role
 */
export async function hasRole(role: Role): Promise<boolean> {
  const profile = await getCurrentUserProfile();
  return profile?.role === role;
}

/**
 * Check if the current user has any of the specified roles
 */
export async function hasAnyRole(roles: Role[]): Promise<boolean> {
  const profile = await getCurrentUserProfile();
  return profile ? roles.includes(profile.role) : false;
}

/**
 * Check if the current user is an admin
 */
export async function isAdmin(): Promise<boolean> {
  return hasRole('admin');
}

/**
 * Check if the current user is staff (admin, manager, or employee)
 */
export async function isStaff(): Promise<boolean> {
  return hasAnyRole(['admin', 'manager', 'employee']);
}

/**
 * Reset password for a user (requires admin privileges)
 */
export async function resetUserPassword(email: string): Promise<boolean> {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    
    if (error) {
      console.error('Error resetting password:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error resetting password:', error);
    return false;
  }
}

/**
 * Change the current user's password
 */
export async function changePassword(newPassword: string): Promise<boolean> {
  try {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });

    if (error) {
      console.error('Error changing password:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error changing password:', error);
    return false;
  }
}
